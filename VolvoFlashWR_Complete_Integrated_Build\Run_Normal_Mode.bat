@echo off
title VolvoFlashWR - Normal Mode
echo === VolvoFlashWR Normal Mode ===
echo.

echo Setting up environment for normal mode...
echo.

REM Set environment variables for library loading
set PATH=%PATH%;%CD%\Libraries;%CD%\Drivers\Vocom
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true

echo Environment configured for normal mode operation
echo.

REM Start the application (try Release first, then Debug)
if exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Starting Release build (x64 with Bridge Service)...
    echo.
    cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"
    "VolvoFlashWR.Launcher.exe"
) else (
    if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
        echo Starting Debug build (x64 with Bridge Service)...
        echo.
        cd /d "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64"
        "VolvoFlashWR.Launcher.exe"
    ) else (
        echo X VolvoFlashWR.Launcher.exe not found!
        echo Please build the application first using: dotnet build --configuration Release
        echo Note: Application now targets x64 architecture with x86 bridge service for Vocom compatibility
        echo.
        pause
        exit /b 1
    )
)

echo.
echo Application closed.
pause
