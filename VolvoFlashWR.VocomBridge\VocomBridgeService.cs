using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.VocomBridge
{
    /// <summary>
    /// Service that handles actual Vocom communication using x86 APCI libraries
    /// This runs in the x86 bridge process to avoid architecture mismatch issues
    /// </summary>
    public class VocomBridgeService : IDisposable
    {
        private readonly ILogger _logger;
        private bool _isInitialized;
        private bool _isConnected;
        private string? _connectedDeviceId;
        private IntPtr _apciHandle = IntPtr.Zero;
        private bool _disposed;

        // APCI library function imports
        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_Initialize();

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_Terminate();

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_ScanDevices(IntPtr deviceList, ref int deviceCount);

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_Connect(string deviceId, ref IntPtr handle);

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_Disconnect(IntPtr handle);

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_SendData(IntPtr handle, byte[] data, int length);

        [DllImport("apci.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int APCI_ReceiveData(IntPtr handle, byte[] buffer, int bufferSize, ref int receivedLength);

        public VocomBridgeService(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Initializes the bridge service and APCI libraries
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom Bridge Service with x86 APCI libraries");

                // Verify that we're running as x86
                if (Environment.Is64BitProcess)
                {
                    _logger.LogError("Bridge service must run as x86 process to be compatible with APCI libraries");
                    return false;
                }

                // Check if APCI libraries are available
                if (!CheckApciLibrariesAvailable())
                {
                    _logger.LogError("APCI libraries not found or not accessible");
                    return false;
                }

                // Initialize APCI
                var result = APCI_Initialize();
                if (result != 0)
                {
                    _logger.LogError($"APCI initialization failed with error code: {result}");
                    return false;
                }

                _isInitialized = true;
                _logger.LogInformation("Vocom Bridge Service initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during bridge service initialization: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        public async Task<List<VocomDevice>> DetectDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                if (!_isInitialized)
                {
                    _logger.LogWarning("Bridge service not initialized, cannot detect devices");
                    return devices;
                }

                _logger.LogInformation("Scanning for Vocom devices using APCI libraries");

                // Allocate memory for device list
                var deviceListSize = 1024; // Adjust size as needed
                var deviceListPtr = Marshal.AllocHGlobal(deviceListSize);
                var deviceCount = 0;

                try
                {
                    var result = APCI_ScanDevices(deviceListPtr, ref deviceCount);
                    if (result == 0 && deviceCount > 0)
                    {
                        _logger.LogInformation($"Found {deviceCount} Vocom devices");

                        // Parse device list (this would need to be implemented based on APCI library documentation)
                        for (int i = 0; i < deviceCount; i++)
                        {
                            var device = new VocomDevice
                            {
                                Id = $"VOCOM_DEVICE_{i + 1}",
                                Name = $"Vocom Device {i + 1}",
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Disconnected
                            };
                            devices.Add(device);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("No Vocom devices found");
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(deviceListPtr);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device detection: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Connects to a specific Vocom device
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId)
        {
            try
            {
                if (!_isInitialized)
                {
                    _logger.LogWarning("Bridge service not initialized, cannot connect to device");
                    return false;
                }

                if (_isConnected)
                {
                    _logger.LogWarning("Already connected to a device, disconnecting first");
                    await DisconnectAsync();
                }

                _logger.LogInformation($"Connecting to Vocom device: {deviceId}");

                var result = APCI_Connect(deviceId, ref _apciHandle);
                if (result == 0 && _apciHandle != IntPtr.Zero)
                {
                    _isConnected = true;
                    _connectedDeviceId = deviceId;
                    _logger.LogInformation($"Successfully connected to device: {deviceId}");
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to connect to device {deviceId}, error code: {result}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the current device
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_isConnected && _apciHandle != IntPtr.Zero)
                {
                    _logger.LogInformation($"Disconnecting from device: {_connectedDeviceId}");

                    var result = APCI_Disconnect(_apciHandle);
                    if (result == 0)
                    {
                        _logger.LogInformation("Successfully disconnected from device");
                    }
                    else
                    {
                        _logger.LogWarning($"Disconnect returned error code: {result}");
                    }

                    _apciHandle = IntPtr.Zero;
                    _isConnected = false;
                    _connectedDeviceId = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disconnect: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends data and receives response
        /// </summary>
        public async Task<byte[]> SendAndReceiveDataAsync(byte[] data)
        {
            try
            {
                if (!_isConnected || _apciHandle == IntPtr.Zero)
                {
                    _logger.LogWarning("Not connected to any device, cannot send data");
                    return new byte[0];
                }

                _logger.LogDebug($"Sending {data.Length} bytes to device");

                // Send data
                var sendResult = APCI_SendData(_apciHandle, data, data.Length);
                if (sendResult != 0)
                {
                    _logger.LogError($"Failed to send data, error code: {sendResult}");
                    return new byte[0];
                }

                // Receive response
                var responseBuffer = new byte[4096]; // Adjust buffer size as needed
                var receivedLength = 0;
                var receiveResult = APCI_ReceiveData(_apciHandle, responseBuffer, responseBuffer.Length, ref receivedLength);

                if (receiveResult == 0 && receivedLength > 0)
                {
                    _logger.LogDebug($"Received {receivedLength} bytes from device");
                    var response = new byte[receivedLength];
                    Array.Copy(responseBuffer, response, receivedLength);
                    return response;
                }
                else
                {
                    _logger.LogWarning($"No data received or receive error, code: {receiveResult}");
                    return new byte[0];
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during data send/receive: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// Checks if APCI libraries are available
        /// </summary>
        private bool CheckApciLibrariesAvailable()
        {
            try
            {
                var appPath = AppDomain.CurrentDomain.BaseDirectory;
                var librariesPath = Path.Combine(appPath, "Libraries");

                var requiredLibraries = new[]
                {
                    "apci.dll",
                    "Volvo.ApciPlus.dll",
                    "Volvo.ApciPlusData.dll"
                };

                foreach (var library in requiredLibraries)
                {
                    var libraryPath = Path.Combine(librariesPath, library);
                    if (!File.Exists(libraryPath))
                    {
                        _logger.LogError($"Required library not found: {libraryPath}");
                        return false;
                    }
                }

                _logger.LogInformation("All required APCI libraries found");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking APCI libraries: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait();
                }

                if (_isInitialized)
                {
                    APCI_Terminate();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }
}
